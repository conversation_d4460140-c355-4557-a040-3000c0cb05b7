#!/usr/bin/env node

/**
 * 测试API验证功能
 * 验证新的API接口是否正常工作
 */

const https = require('https');
const querystring = require('querystring');

// 测试配置
const TEST_CONFIG = {
  apiUrl: 'https://xiaomeihuakefu.cn/api/verify.php',
  timeout: 10000,
  testCases: [
    {
      name: '空卡密测试',
      data: { key: '' },
      expectedSuccess: false
    },
    {
      name: '无效卡密测试',
      data: { key: 'invalid_key_12345' },
      expectedSuccess: false
    },
    {
      name: '格式错误卡密测试',
      data: { key: 'test' },
      expectedSuccess: false
    }
  ]
};

/**
 * 发送HTTP POST请求
 */
function makeRequest(url, data) {
  return new Promise((resolve, reject) => {
    const postData = querystring.stringify(data);
    const urlObj = new URL(url);
    
    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port || 443,
      path: urlObj.pathname,
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Content-Length': Buffer.byteLength(postData),
        'User-Agent': 'XiaomeihuaApp/1.0.0'
      },
      timeout: TEST_CONFIG.timeout
    };

    const req = https.request(options, (res) => {
      let body = '';
      
      res.on('data', (chunk) => {
        body += chunk;
      });
      
      res.on('end', () => {
        try {
          const data = JSON.parse(body);
          resolve({
            statusCode: res.statusCode,
            headers: res.headers,
            data: data
          });
        } catch (error) {
          reject(new Error(`JSON解析失败: ${error.message}, 响应: ${body}`));
        }
      });
    });

    req.on('error', (error) => {
      reject(new Error(`请求失败: ${error.message}`));
    });

    req.on('timeout', () => {
      req.destroy();
      reject(new Error('请求超时'));
    });

    req.write(postData);
    req.end();
  });
}

/**
 * 运行单个测试用例
 */
async function runTestCase(testCase) {
  console.log(`\n🧪 运行测试: ${testCase.name}`);
  console.log(`📤 请求数据:`, testCase.data);
  
  try {
    const response = await makeRequest(TEST_CONFIG.apiUrl, testCase.data);
    
    console.log(`📥 响应状态: ${response.statusCode}`);
    console.log(`📥 响应数据:`, JSON.stringify(response.data, null, 2));
    
    // 验证响应结构
    if (!response.data || typeof response.data.success !== 'boolean') {
      console.log(`❌ 响应格式错误: 缺少success字段`);
      return false;
    }
    
    // 验证预期结果
    if (response.data.success === testCase.expectedSuccess) {
      console.log(`✅ 测试通过: 结果符合预期`);
      return true;
    } else {
      console.log(`❌ 测试失败: 预期 ${testCase.expectedSuccess}, 实际 ${response.data.success}`);
      return false;
    }
    
  } catch (error) {
    console.log(`❌ 测试失败: ${error.message}`);
    return false;
  }
}

/**
 * 测试API连通性
 */
async function testApiConnectivity() {
  console.log(`🔗 测试API连通性: ${TEST_CONFIG.apiUrl}`);

  try {
    const response = await makeRequest(TEST_CONFIG.apiUrl, { key: 'connectivity_test' });
    console.log(`✅ API连通性正常, 状态码: ${response.statusCode}`);
    console.log(`📥 连通性测试响应:`, response.data);
    return true;
  } catch (error) {
    console.log(`❌ API连通性失败: ${error.message}`);
    return false;
  }
}

/**
 * 主测试函数
 */
async function runTests() {
  console.log('🚀 开始API验证功能测试');
  console.log('=' * 50);
  
  // 测试API连通性
  const connectivityOk = await testApiConnectivity();
  if (!connectivityOk) {
    console.log('\n❌ API连通性测试失败，终止测试');
    process.exit(1);
  }
  
  // 运行所有测试用例
  let passedTests = 0;
  let totalTests = TEST_CONFIG.testCases.length;
  
  for (const testCase of TEST_CONFIG.testCases) {
    const passed = await runTestCase(testCase);
    if (passed) {
      passedTests++;
    }
  }
  
  // 输出测试结果
  console.log('\n' + '=' * 50);
  console.log(`📊 测试结果: ${passedTests}/${totalTests} 通过`);
  
  if (passedTests === totalTests) {
    console.log('🎉 所有测试通过！API验证功能正常');
    process.exit(0);
  } else {
    console.log('⚠️  部分测试失败，请检查API实现');
    process.exit(1);
  }
}

// 运行测试
if (require.main === module) {
  runTests().catch(error => {
    console.error('测试运行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runTests,
  makeRequest,
  TEST_CONFIG
};
